# Include all requirements to run the bot.
-r requirements.txt
-r requirements-plot.txt
-r requirements-hyperopt.txt
-r requirements-freqai.txt
-r requirements-freqai-rl.txt
-r docs/requirements-docs.txt

coveralls==4.0.1
ruff==0.11.11
mypy==1.15.0
pre-commit==4.2.0
pytest==8.3.5
pytest-asyncio==0.26.0
pytest-cov==6.1.1
pytest-mock==3.14.0
pytest-random-order==1.1.1
pytest-timeout==2.4.0
pytest-xdist==3.6.1
isort==6.0.1
# For datetime mocking
time-machine==2.16.0

# Convert jupyter notebooks to markdown documents
nbconvert==7.16.6

# mypy types
types-cachetools==6.0.0.20250525
types-filelock==3.2.7
types-requests==2.32.0.20250515
types-tabulate==0.9.0.20241207
types-python-dateutil==2.9.0.20250516
