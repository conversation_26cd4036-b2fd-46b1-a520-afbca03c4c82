"""
Custom Hyperliquid exchange with XBIT historical data support
Extends the standard Hyperliquid exchange to support FreqAI
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timezone

from freqtrade.exchange import Exchange
from freqtrade.exchange.hyperliquid import Hyperliquid
from freqtrade.exceptions import OperationalException

logger = logging.getLogger(__name__)

class Hyperliquid_xbit(Hyperliquid):
    """
    Hyperliquid exchange with XBIT historical data support
    """
    
    def __init__(self, config: Dict, validate: bool = True, load_leverage_tiers: bool = True) -> None:
        # Temporarily change exchange name to 'hyperliquid' for CCXT initialization
        original_name = config['exchange']['name']
        config['exchange']['name'] = 'hyperliquid'

        super().__init__(config, validate=validate)

        # Restore original name
        config['exchange']['name'] = original_name
        
        # Import XBIT data handler
        try:
            import sys
            import os
            # Add the user_data/data directory to path
            user_data_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'user_data', 'data')
            if user_data_dir not in sys.path:
                sys.path.append(user_data_dir)

            from xbit_data_handler import XBITDataHandler
            self.xbit_handler = XBITDataHandler()
            logger.info("XBIT data handler initialized successfully")
        except ImportError as e:
            logger.error(f"Failed to import XBIT data handler: {e}")
            self.xbit_handler = None
    
    @property
    def name(self) -> str:
        return "hyperliquid_xbit"
    
    def get_historic_ohlcv(self, pair: str, timeframe: str, since_ms: int = None, 
                          is_new_pair: bool = False, raise_: bool = False,
                          candle_type: str = '') -> List[List]:
        """
        Get historic OHLCV data using XBIT API
        """
        if not self.xbit_handler:
            if raise_:
                raise OperationalException("XBIT data handler not available")
            return []
        
        try:
            logger.info(f"Fetching historic data for {pair} {timeframe} using XBIT API")
            return self.xbit_handler.get_historic_ohlcv(pair, timeframe, since_ms)
        except Exception as e:
            logger.error(f"Failed to fetch historic data from XBIT: {e}")
            if raise_:
                raise OperationalException(f"XBIT data fetch failed: {e}")
            return []
    
    def get_historic_ohlcv_as_df(self, pair: str, timeframe: str, since_ms: int = None,
                                candle_type: str = ''):
        """
        Get historic OHLCV data as DataFrame using XBIT API
        """
        if not self.xbit_handler:
            raise OperationalException("XBIT data handler not available")
        
        logger.info(f"Fetching historic data as DataFrame for {pair} {timeframe}")
        return self.xbit_handler.get_historic_ohlcv_as_df(pair, timeframe, since_ms)
    
    def refresh_latest_ohlcv(self, pair_list: List[Tuple[str, str, str]], 
                           since_ms: Optional[int] = None, cache: bool = True,
                           drop_incomplete: bool = None) -> Dict[Tuple[str, str, str], List]:
        """
        Refresh OHLCV data for multiple pairs using XBIT API for historical data
        and live data from Hyperliquid
        """
        logger.debug(f"Refreshing OHLCV for {len(pair_list)} pairs")
        
        result = {}
        
        for pair, timeframe, candle_type in pair_list:
            try:
                # For recent data, try to use live Hyperliquid API
                # For older data, use XBIT API
                if since_ms and since_ms < (datetime.now(timezone.utc).timestamp() - 24 * 60 * 60) * 1000:
                    # Use XBIT for data older than 24 hours
                    ohlcv = self.get_historic_ohlcv(pair, timeframe, since_ms)
                else:
                    # Try live API first, fallback to XBIT
                    try:
                        ohlcv = super().get_historic_ohlcv(pair, timeframe, since_ms)
                    except:
                        logger.info(f"Live API failed for {pair}, using XBIT")
                        ohlcv = self.get_historic_ohlcv(pair, timeframe, since_ms)
                
                result[(pair, timeframe, candle_type)] = ohlcv
                
            except Exception as e:
                logger.error(f"Failed to refresh OHLCV for {pair} {timeframe}: {e}")
                result[(pair, timeframe, candle_type)] = []
        
        return result
    
    @property
    def ohlcv_candle_limit(self, timeframe: str, candle_type: str = '', since_ms: int = None) -> int:
        """
        Return candle limit for OHLCV requests
        XBIT API supports up to 10000 candles per request
        """
        return 10000
    
    def _get_ohlcv(self, pair: str, timeframe: str, limit: int = 100, since_ms: int = None,
                   is_new_pair: bool = False, raise_: bool = False, candle_type: str = '') -> List:
        """
        Internal method to get OHLCV data
        """
        return self.get_historic_ohlcv(pair, timeframe, since_ms, is_new_pair, raise_, candle_type)
